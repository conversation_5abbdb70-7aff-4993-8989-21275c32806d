#!/usr/bin/env python3
"""测试GUI修复的脚本。"""

import flet as ft
import asyncio
import aiohttp
import time
from beezer.plugins.utils import get_server_port


def main(page: ft.Page):
    """主函数。"""
    page.title = "Beezer GUI修复测试"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 20
    
    # 创建ListView测试
    status_list = ft.ListView(expand=True, spacing=5, padding=10)
    data_list = ft.ListView(expand=True, spacing=5, padding=10)
    
    # 状态显示
    status_text = ft.Text("正在测试...", size=16)
    
    async def test_listview_update():
        """测试ListView更新。"""
        try:
            # 清空列表
            status_list.controls.clear()
            data_list.controls.clear()
            
            # 添加测试项目到状态列表
            for i in range(5):
                status_list.controls.append(
                    ft.Card(
                        content=ft.Container(
                            content=ft.Text(f"状态项目 {i+1}", size=14),
                            padding=10,
                        )
                    )
                )
            
            # 添加测试项目到数据列表
            for i in range(3):
                data_list.controls.append(
                    ft.Card(
                        content=ft.Container(
                            content=ft.Column([
                                ft.Text(f"数据项目 {i+1}", weight=ft.FontWeight.BOLD),
                                ft.Text(f"时间: {time.strftime('%H:%M:%S')}", size=12),
                            ]),
                            padding=10,
                        )
                    )
                )
            
            status_text.value = "✅ ListView更新测试完成"
            status_text.color = ft.Colors.GREEN
            
            # 关键：调用page.update()
            page.update()
            
        except Exception as e:
            status_text.value = f"❌ 测试失败: {e}"
            status_text.color = ft.Colors.RED
            page.update()
    
    async def test_api_connection():
        """测试API连接。"""
        try:
            server_port = get_server_port()
            api_url = f"http://localhost:{server_port}/api"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{api_url}/plugin/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        plugin_count = len(data.get("plugins", {}))
                        
                        # 添加API结果到列表
                        status_list.controls.append(
                            ft.Card(
                                content=ft.Container(
                                    content=ft.Text(f"API连接成功 - {plugin_count}个插件", color=ft.Colors.GREEN),
                                    padding=10,
                                )
                            )
                        )
                        
                        page.update()
                    else:
                        raise Exception(f"API返回错误: {response.status}")
                        
        except Exception as e:
            status_list.controls.append(
                ft.Card(
                    content=ft.Container(
                        content=ft.Text(f"API连接失败: {e}", color=ft.Colors.RED),
                        padding=10,
                    )
                )
            )
            page.update()
    
    async def on_test_click(e):
        """测试按钮点击事件。"""
        await test_listview_update()
        await test_api_connection()
    
    # 测试按钮
    test_button = ft.ElevatedButton(
        text="运行测试",
        icon=ft.Icons.PLAY_ARROW,
        on_click=on_test_click,
    )
    
    # 布局
    page.add(
        ft.Text("Beezer GUI修复测试", size=24, weight=ft.FontWeight.BOLD),
        ft.Divider(),
        ft.Row([status_text, ft.Container(expand=True), test_button]),
        ft.Divider(),
        ft.Row([
            ft.Container(
                content=ft.Column([
                    ft.Text("状态列表测试", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=status_list,
                        height=200,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=5,
                    ),
                ]),
                expand=True,
            ),
            ft.Container(width=20),
            ft.Container(
                content=ft.Column([
                    ft.Text("数据列表测试", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=data_list,
                        height=200,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=5,
                    ),
                ]),
                expand=True,
            ),
        ]),
    )
    
    # 自动运行测试
    page.run_task(test_listview_update)


if __name__ == "__main__":
    print("启动GUI修复测试...")
    print("请访问 http://localhost:8086 查看界面")
    ft.app(target=main, port=8086, view=ft.AppView.WEB_BROWSER)
