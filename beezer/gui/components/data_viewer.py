"""数据查看组件。

提供数据展示、过滤、搜索等功能。
"""

import asyncio
import flet as ft
import json
import time
from typing import Dict, Any, List, Optional, Callable
from loguru import logger


class DataViewer:
    """数据查看组件，用于展示插件数据。"""

    def __init__(
        self,
        title: str = "数据监控",
        on_refresh: Optional[Callable[[], None]] = None,
        on_clear: Optional[Callable[[], None]] = None,
    ):
        """初始化数据查看器。

        Args:
            title: 标题
            on_refresh: 刷新回调函数
            on_clear: 清除回调函数
        """
        self.title = title
        self.on_refresh = on_refresh
        self.on_clear = on_clear

        # 数据存储
        self.data_entries: List[Dict[str, Any]] = []
        self.filtered_entries: List[Dict[str, Any]] = []

        # UI组件
        self.data_list = ft.ListView(expand=True, spacing=5, padding=10)
        self.status_text = ft.Text("暂无数据", size=14, color=ft.Colors.GREY_600)
        self.filter_plugin_id = ft.TextField(
            label="插件ID过滤",
            hint_text="输入插件ID",
            on_change=self._on_filter_change,
            width=200,
        )
        self.filter_data_type = ft.Dropdown(
            label="数据类型",
            options=[
                ft.dropdown.Option("", "全部"),
                ft.dropdown.Option("input", "输入数据"),
                ft.dropdown.Option("output", "输出数据"),
                ft.dropdown.Option("processed", "处理数据"),
            ],
            value="",
            on_change=self._on_filter_change,
            width=150,
        )
        self.auto_refresh_switch = ft.Switch(
            label="自动刷新",
            value=False,
            on_change=self._on_auto_refresh_change,
        )

        # 自动刷新定时器
        self._auto_refresh_timer = None

    def build(self):
        """构建数据查看器界面。"""
        # 创建工具栏
        toolbar = ft.Row(
            [
                ft.Text(self.title, size=20, weight=ft.FontWeight.BOLD),
                ft.Container(expand=True),  # 占位符
                self.filter_plugin_id,
                self.filter_data_type,
                self.auto_refresh_switch,
                ft.ElevatedButton(
                    text="刷新",
                    icon=ft.Icons.REFRESH,
                    on_click=self._on_refresh_click,
                ),
                ft.OutlinedButton(
                    text="清除",
                    icon=ft.Icons.CLEAR,
                    on_click=self._on_clear_click,
                ),
            ]
        )

        # 创建状态栏
        status_bar = ft.Row(
            [
                self.status_text,
                ft.Container(expand=True),
                ft.Text(
                    f"最后更新: {time.strftime('%H:%M:%S')}",
                    size=12,
                    color=ft.Colors.GREY_500,
                ),
            ]
        )

        # 创建主容器
        return ft.Column(
            [
                toolbar,
                ft.Divider(),
                status_bar,
                ft.Container(
                    content=self.data_list,
                    expand=True,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=5,
                ),
            ]
        )

    def update_data(self, data_entries: List[Dict[str, Any]]):
        """更新数据。

        Args:
            data_entries: 数据条目列表
        """
        self.data_entries = data_entries
        self._apply_filters()
        self._refresh_list()
        self._update_status()

    def _apply_filters(self):
        """应用过滤条件。"""
        filtered = self.data_entries

        # 按插件ID过滤
        plugin_id_filter = self.filter_plugin_id.value.strip()
        if plugin_id_filter:
            filtered = [
                entry
                for entry in filtered
                if plugin_id_filter.lower() in entry.get("plugin_id", "").lower()
            ]

        # 按数据类型过滤
        data_type_filter = self.filter_data_type.value
        if data_type_filter:
            filtered = [
                entry
                for entry in filtered
                if entry.get("data_type") == data_type_filter
            ]

        self.filtered_entries = filtered

    def _refresh_list(self):
        """刷新数据列表。"""
        self.data_list.controls.clear()

        for entry in self.filtered_entries[-50:]:  # 只显示最新的50条
            self.data_list.controls.append(self._create_data_card(entry))

        if hasattr(self.data_list, "update"):
            self.data_list.update()

    def _create_data_card(self, entry: Dict[str, Any]) -> ft.Card:
        """创建数据卡片。

        Args:
            entry: 数据条目

        Returns:
            数据卡片组件
        """
        plugin_id = entry.get("plugin_id", "未知")
        data_type = entry.get("data_type", "未知")
        timestamp = entry.get("timestamp", 0)
        data = entry.get("data", {})
        metadata = entry.get("metadata", {})

        # 格式化时间
        time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))

        # 格式化数据（限制长度）
        data_str = json.dumps(data, ensure_ascii=False, indent=2)
        if len(data_str) > 200:
            data_str = data_str[:200] + "..."

        # 创建元数据显示
        metadata_str = ""
        if metadata:
            metadata_str = f"元数据: {json.dumps(metadata, ensure_ascii=False)}"

        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Text(
                                    f"{plugin_id}",
                                    size=14,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Container(
                                    content=ft.Text(
                                        data_type,
                                        size=12,
                                        color=ft.Colors.WHITE,
                                    ),
                                    bgcolor=self._get_data_type_color(data_type),
                                    padding=ft.padding.symmetric(
                                        horizontal=8, vertical=2
                                    ),
                                    border_radius=10,
                                ),
                                ft.Container(expand=True),
                                ft.Text(
                                    time_str,
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                ),
                            ]
                        ),
                        ft.Container(
                            content=ft.Text(
                                data_str,
                                size=12,
                                selectable=True,
                            ),
                            bgcolor=ft.Colors.GREY_100,
                            padding=10,
                            border_radius=5,
                        ),
                        ft.Text(
                            metadata_str,
                            size=10,
                            color=ft.Colors.GREY_500,
                        )
                        if metadata_str
                        else ft.Container(),
                    ],
                    spacing=5,
                ),
                padding=15,
            ),
        )

    def _get_data_type_color(self, data_type: str) -> str:
        """根据数据类型获取颜色。

        Args:
            data_type: 数据类型

        Returns:
            颜色值
        """
        color_map = {
            "input": ft.Colors.BLUE,
            "output": ft.Colors.GREEN,
            "processed": ft.Colors.ORANGE,
        }
        return color_map.get(data_type, ft.Colors.GREY)

    def _update_status(self):
        """更新状态文本。"""
        total_count = len(self.data_entries)
        filtered_count = len(self.filtered_entries)

        if total_count == 0:
            self.status_text.value = "暂无数据"
        elif filtered_count == total_count:
            self.status_text.value = f"共 {total_count} 条数据"
        else:
            self.status_text.value = f"显示 {filtered_count} / {total_count} 条数据"

        if hasattr(self.status_text, "update"):
            self.status_text.update()

    def _on_filter_change(self, e):
        """过滤条件变化事件。"""
        self._apply_filters()
        self._refresh_list()
        self._update_status()

    async def _on_refresh_click(self, e):
        """刷新按钮点击事件。"""
        if self.on_refresh:
            if asyncio.iscoroutinefunction(self.on_refresh):
                await self.on_refresh()
            else:
                self.on_refresh()

    def _on_clear_click(self, e):
        """清除按钮点击事件。"""
        if self.on_clear:
            self.on_clear()

    def _on_auto_refresh_change(self, e):
        """自动刷新开关变化事件。"""
        if self.auto_refresh_switch.value:
            self._start_auto_refresh()
        else:
            self._stop_auto_refresh()

    def _start_auto_refresh(self):
        """开始自动刷新。"""
        # TODO: 实现自动刷新定时器
        logger.info("自动刷新已启用")

    def _stop_auto_refresh(self):
        """停止自动刷新。"""
        # TODO: 停止自动刷新定时器
        logger.info("自动刷新已禁用")
