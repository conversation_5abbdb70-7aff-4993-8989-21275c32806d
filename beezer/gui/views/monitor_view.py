"""数据监控视图。

提供实时数据监控、插件状态查看等功能。
"""

import flet as ft
import aiohttp
import asyncio
import time
from typing import Dict, Any, List
from loguru import logger

from beezer.gui.components.data_viewer import DataViewer


def get_server_port() -> int:
    """获取服务器端口配置

    Returns:
        int: 服务器端口，默认为9999
    """
    try:
        # 尝试直接读取配置文件
        import yaml
        from beezer.type_model import ConfigModel

        config_paths = ["/app/config.yaml", "config.yaml"]
        for config_path in config_paths:
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = yaml.safe_load(f)
                config = ConfigModel.model_validate(config_data)
                return config.server_port
            except Exception:
                continue

    except Exception as e:
        logger.warning(f"无法获取服务器端口配置，使用默认值9999: {e}")

    # 默认端口
    return 9999


class MonitorView:
    """数据监控视图。"""

    def __init__(self, page: ft.Page):
        """初始化监控视图。

        Args:
            page: Flet页面对象
        """
        self.page = page
        # 从配置文件读取端口并构建API基础URL
        server_port = get_server_port()
        self.api_base_url = f"http://localhost:{server_port}/api"

        # 数据存储
        self.plugin_status: Dict[str, Any] = {}
        self.plugin_data: Dict[str, List[Dict[str, Any]]] = {}

        # UI组件
        self.status_list = ft.ListView(expand=True, spacing=5, padding=10)
        self.data_viewer = DataViewer(
            title="实时数据监控",
            on_refresh=self._refresh_data,
            on_clear=self._clear_data,
        )
        # 设置page引用
        self.data_viewer.page = page
        self.page = page

        # 状态统计
        self.total_plugins_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)
        self.running_plugins_text = ft.Text(
            "0", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN
        )
        self.error_plugins_text = ft.Text(
            "0", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.RED
        )
        self.total_data_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)

    def build(self):
        """构建监控视图界面。"""
        # 创建状态统计卡片
        stats_cards = ft.Row(
            [
                self._create_stat_card(
                    "总插件数", self.total_plugins_text, ft.Icons.EXTENSION
                ),
                self._create_stat_card(
                    "运行中", self.running_plugins_text, ft.Icons.PLAY_CIRCLE
                ),
                self._create_stat_card("错误", self.error_plugins_text, ft.Icons.ERROR),
                self._create_stat_card(
                    "数据条目", self.total_data_text, ft.Icons.DATA_USAGE
                ),
            ],
            spacing=20,
        )

        # 创建标签页
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="插件状态",
                    icon=ft.Icons.SETTINGS,
                    content=ft.Container(
                        content=ft.Column(
                            [
                                ft.Row(
                                    [
                                        ft.Text(
                                            "插件状态监控",
                                            size=18,
                                            weight=ft.FontWeight.BOLD,
                                        ),
                                        ft.Container(expand=True),
                                        ft.ElevatedButton(
                                            text="刷新状态",
                                            icon=ft.Icons.REFRESH,
                                            on_click=self._on_refresh_status_click,
                                        ),
                                    ]
                                ),
                                ft.Divider(),
                                ft.Container(
                                    content=self.status_list,
                                    expand=True,
                                    border=ft.border.all(1, ft.Colors.GREY_300),
                                    border_radius=5,
                                ),
                            ]
                        ),
                        padding=20,
                    ),
                ),
                ft.Tab(
                    text="数据监控",
                    icon=ft.Icons.MONITOR,
                    content=ft.Container(
                        content=self.data_viewer.build(),
                        padding=20,
                    ),
                ),
            ],
            expand=True,
        )

        # 创建主容器
        return ft.Column(
            [
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text(
                                "Beezer 数据监控", size=28, weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                "实时监控插件状态和数据流",
                                size=14,
                                color=ft.Colors.GREY_600,
                            ),
                        ]
                    ),
                    padding=ft.padding.only(bottom=20),
                ),
                stats_cards,
                ft.Container(height=20),  # 间距
                tabs,
            ]
        )

    def _create_stat_card(self, title: str, value_text: ft.Text, icon: str) -> ft.Card:
        """创建状态统计卡片。

        Args:
            title: 标题
            value_text: 数值文本组件
            icon: 图标

        Returns:
            统计卡片
        """
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Icon(icon, size=40, color=ft.Colors.BLUE),
                        ft.Container(width=10),
                        ft.Column(
                            [
                                value_text,
                                ft.Text(title, size=12, color=ft.Colors.GREY_600),
                            ],
                            spacing=0,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.START,
                ),
                padding=20,
                width=200,
            ),
        )

    async def did_mount(self):
        """组件挂载后的回调。"""
        await self._refresh_all()

    async def _on_refresh_status_click(self, e):
        """刷新状态按钮点击事件。"""
        await self._refresh_status()

    async def _refresh_all(self):
        """刷新所有数据。"""
        await asyncio.gather(
            self._refresh_status(),
            self._refresh_data(),
        )

    async def _refresh_status(self, e=None):
        """刷新插件状态。"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base_url}/plugin/status"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.plugin_status = data.get("plugins", {})
                        self._update_status_list()
                        self._update_stats()
                    else:
                        logger.error(f"获取插件状态失败: {response.status}")
        except Exception as e:
            logger.error(f"刷新插件状态时出错: {e}")

    async def _refresh_data(self, e=None):
        """刷新插件数据。"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base_url}/plugin/data?limit=200"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.plugin_data = data.get("plugins", {})

                        # 将所有数据合并为一个列表
                        all_data = []
                        for plugin_id, entries in self.plugin_data.items():
                            all_data.extend(entries)

                        # 按时间戳排序
                        all_data.sort(key=lambda x: x.get("timestamp", 0))

                        self.data_viewer.update_data(all_data)
                        self._update_stats()
                    else:
                        logger.error(f"获取插件数据失败: {response.status}")
        except Exception as e:
            logger.error(f"刷新插件数据时出错: {e}")

    def _clear_data(self):
        """清除数据。"""
        # TODO: 实现清除数据功能，可能需要调用API
        logger.info("清除数据功能待实现")

    def _update_status_list(self):
        """更新状态列表。"""
        self.status_list.controls.clear()

        for plugin_id, status_info in self.plugin_status.items():
            self.status_list.controls.append(
                self._create_status_card(plugin_id, status_info)
            )

        # 更新页面
        if hasattr(self, "page") and self.page:
            self.page.update()

    def _create_status_card(
        self, plugin_id: str, status_info: Dict[str, Any]
    ) -> ft.Card:
        """创建状态卡片。

        Args:
            plugin_id: 插件ID
            status_info: 状态信息

        Returns:
            状态卡片
        """
        plugin_type = status_info.get("plugin_type", "未知")
        status = status_info.get("status", "未知")
        timestamp = status_info.get("timestamp", 0)
        details = status_info.get("details", {})

        # 格式化时间
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))

        # 状态颜色
        status_color = self._get_status_color(status)

        # 详细信息
        details_str = ""
        if details:
            details_items = [f"{k}: {v}" for k, v in details.items()]
            details_str = " | ".join(details_items[:3])  # 只显示前3个

        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Column(
                            [
                                ft.Text(
                                    plugin_id,
                                    size=16,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Text(
                                    f"类型: {plugin_type}",
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                ),
                                ft.Text(
                                    details_str,
                                    size=10,
                                    color=ft.Colors.GREY_500,
                                )
                                if details_str
                                else ft.Container(),
                            ],
                            expand=True,
                        ),
                        ft.Column(
                            [
                                ft.Container(
                                    content=ft.Text(
                                        status,
                                        size=12,
                                        color=ft.Colors.WHITE,
                                        weight=ft.FontWeight.BOLD,
                                    ),
                                    bgcolor=status_color,
                                    padding=ft.padding.symmetric(
                                        horizontal=12, vertical=4
                                    ),
                                    border_radius=15,
                                ),
                                ft.Text(
                                    time_str,
                                    size=10,
                                    color=ft.Colors.GREY_500,
                                ),
                            ],
                            horizontal_alignment=ft.CrossAxisAlignment.END,
                        ),
                    ]
                ),
                padding=15,
            ),
        )

    def _get_status_color(self, status: str) -> str:
        """根据状态获取颜色。

        Args:
            status: 状态

        Returns:
            颜色值
        """
        color_map = {
            "running": ft.Colors.GREEN,
            "idle": ft.Colors.BLUE,
            "error": ft.Colors.RED,
            "stopped": ft.Colors.GREY,
        }
        return color_map.get(status.lower(), ft.Colors.ORANGE)

    def _update_stats(self):
        """更新统计信息。"""
        # 插件状态统计
        total_plugins = len(self.plugin_status)
        running_plugins = sum(
            1 for s in self.plugin_status.values() if s.get("status") == "running"
        )
        error_plugins = sum(
            1 for s in self.plugin_status.values() if s.get("status") == "error"
        )

        # 数据统计
        total_data = sum(len(entries) for entries in self.plugin_data.values())

        # 更新UI
        self.total_plugins_text.value = str(total_plugins)
        self.running_plugins_text.value = str(running_plugins)
        self.error_plugins_text.value = str(error_plugins)
        self.total_data_text.value = str(total_data)

        # 更新页面
        if hasattr(self, "page") and self.page:
            self.page.update()
