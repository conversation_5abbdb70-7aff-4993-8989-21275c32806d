#!/usr/bin/env python3
"""最小化的监控界面测试。"""

import flet as ft
import asyncio
import aiohttp
import time
from beezer.plugins.utils import get_server_port


def main(page: ft.Page):
    """主函数。"""
    page.title = "Beezer 最小监控测试"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 20
    
    # 状态显示
    status_text = ft.Text("正在连接...", size=16)
    plugin_count_text = ft.Text("插件数: 0", size=14)
    data_count_text = ft.Text("数据条目: 0", size=14)
    last_update_text = ft.Text("最后更新: --", size=12, color=ft.Colors.GREY_600)
    
    # 数据显示区域
    data_display = ft.Text("暂无数据", size=12)
    
    async def refresh_data():
        """刷新数据。"""
        try:
            server_port = get_server_port()
            api_url = f"http://localhost:{server_port}/api"
            
            status_text.value = f"连接到 {api_url}"
            status_text.color = ft.Colors.BLUE
            page.update()
            
            async with aiohttp.ClientSession() as session:
                # 获取插件状态
                async with session.get(f"{api_url}/plugin/status") as response:
                    if response.status == 200:
                        status_data = await response.json()
                        plugins = status_data.get("plugins", {})
                        plugin_count_text.value = f"插件数: {len(plugins)}"
                        
                        status_text.value = f"已连接 - {len(plugins)} 个插件"
                        status_text.color = ft.Colors.GREEN
                        
                        # 显示插件状态
                        plugin_info = []
                        for plugin_id, info in plugins.items():
                            status = info.get("status", "未知")
                            plugin_info.append(f"{plugin_id}: {status}")
                        
                        if plugin_info:
                            data_display.value = "插件状态:\n" + "\n".join(plugin_info)
                        
                    else:
                        status_text.value = f"状态API失败: {response.status}"
                        status_text.color = ft.Colors.RED
                
                # 获取插件数据
                async with session.get(f"{api_url}/plugin/data?limit=5") as response:
                    if response.status == 200:
                        data = await response.json()
                        plugin_data = data.get("plugins", {})
                        total_count = data.get("count", 0)
                        
                        data_count_text.value = f"数据条目: {total_count}"
                        
                        # 合并所有数据
                        all_data = []
                        for plugin_id, entries in plugin_data.items():
                            all_data.extend(entries)
                        
                        # 按时间戳排序
                        all_data.sort(key=lambda x: x.get("timestamp", 0), reverse=True)
                        
                        # 显示最新数据
                        if all_data:
                            data_info = [data_display.value, "\n最新数据:"]
                            for i, entry in enumerate(all_data[:3]):  # 显示最新3条
                                plugin_id = entry.get("plugin_id", "未知")
                                data_type = entry.get("data_type", "未知")
                                timestamp = entry.get("timestamp", 0)
                                time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))
                                data_info.append(f"{i+1}. {plugin_id} ({data_type}) - {time_str}")
                            
                            data_display.value = "\n".join(data_info)
                        
                    else:
                        data_count_text.value = f"数据API失败: {response.status}"
                        
            # 更新时间戳
            last_update_text.value = f"最后更新: {time.strftime('%H:%M:%S')}"
                        
        except Exception as e:
            status_text.value = f"错误: {str(e)}"
            status_text.color = ft.Colors.RED
        
        page.update()
    
    async def on_refresh_click(e):
        """刷新按钮点击事件。"""
        await refresh_data()
    
    # 刷新按钮
    refresh_button = ft.ElevatedButton(
        text="刷新",
        icon=ft.Icons.REFRESH,
        on_click=on_refresh_click,
    )
    
    # 布局
    page.add(
        ft.Text("Beezer 最小监控测试", size=24, weight=ft.FontWeight.BOLD),
        ft.Divider(),
        ft.Row([status_text, ft.Container(expand=True), refresh_button]),
        ft.Row([plugin_count_text, data_count_text]),
        last_update_text,
        ft.Divider(),
        ft.Container(
            content=ft.Column([
                ft.Text("数据详情:", weight=ft.FontWeight.BOLD),
                data_display,
            ]),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=5,
        ),
    )
    
    # 初始化数据
    page.run_task(refresh_data)


if __name__ == "__main__":
    print("启动最小监控界面测试...")
    print("请访问 http://localhost:8084 查看界面")
    ft.app(target=main, port=8084, view=ft.AppView.WEB_BROWSER)
