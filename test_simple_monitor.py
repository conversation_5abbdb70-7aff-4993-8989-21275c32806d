#!/usr/bin/env python3
"""简单的监控界面测试。"""

import flet as ft
import asyncio
import aiohttp
import time
from beezer.plugins.utils import get_server_port


def main(page: ft.Page):
    """主函数。"""
    page.title = "Beezer 数据监控测试"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 20
    
    # 状态显示
    status_text = ft.Text("正在连接...", size=16)
    data_count_text = ft.Text("数据条目: 0", size=14)
    last_update_text = ft.Text("最后更新: --", size=12, color=ft.Colors.GREY_600)
    
    # 数据列表
    data_list = ft.ListView(expand=True, spacing=5, padding=10)
    
    async def refresh_data():
        """刷新数据。"""
        try:
            server_port = get_server_port()
            api_url = f"http://localhost:{server_port}/api"
            
            async with aiohttp.ClientSession() as session:
                # 获取插件状态
                async with session.get(f"{api_url}/plugin/status") as response:
                    if response.status == 200:
                        status_data = await response.json()
                        plugin_count = len(status_data.get("plugins", {}))
                        status_text.value = f"已连接 - {plugin_count} 个插件"
                        status_text.color = ft.Colors.GREEN
                    else:
                        status_text.value = "连接失败"
                        status_text.color = ft.Colors.RED
                
                # 获取插件数据
                async with session.get(f"{api_url}/plugin/data?limit=10") as response:
                    if response.status == 200:
                        data = await response.json()
                        plugin_data = data.get("plugins", {})
                        
                        # 合并所有数据
                        all_data = []
                        for plugin_id, entries in plugin_data.items():
                            all_data.extend(entries)
                        
                        # 按时间戳排序
                        all_data.sort(key=lambda x: x.get("timestamp", 0), reverse=True)
                        
                        # 更新数据计数
                        data_count_text.value = f"数据条目: {len(all_data)}"
                        
                        # 更新数据列表
                        data_list.controls.clear()
                        for i, entry in enumerate(all_data[:5]):  # 只显示最新的5条
                            plugin_id = entry.get("plugin_id", "未知")
                            data_type = entry.get("data_type", "未知")
                            timestamp = entry.get("timestamp", 0)
                            
                            time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))
                            
                            card = ft.Card(
                                content=ft.Container(
                                    content=ft.Column([
                                        ft.Row([
                                            ft.Text(plugin_id, weight=ft.FontWeight.BOLD),
                                            ft.Container(expand=True),
                                            ft.Text(time_str, size=12, color=ft.Colors.GREY_600),
                                        ]),
                                        ft.Text(f"类型: {data_type}", size=12),
                                    ]),
                                    padding=10,
                                )
                            )
                            data_list.controls.append(card)
                        
                        # 更新时间戳
                        last_update_text.value = f"最后更新: {time.strftime('%H:%M:%S')}"
                        
                    else:
                        data_count_text.value = "数据获取失败"
                        
        except Exception as e:
            status_text.value = f"错误: {str(e)}"
            status_text.color = ft.Colors.RED
        
        page.update()
    
    async def on_refresh_click(e):
        """刷新按钮点击事件。"""
        await refresh_data()
    
    # 刷新按钮
    refresh_button = ft.ElevatedButton(
        text="刷新",
        icon=ft.Icons.REFRESH,
        on_click=on_refresh_click,
    )
    
    # 布局
    page.add(
        ft.Text("Beezer 数据监控测试", size=24, weight=ft.FontWeight.BOLD),
        ft.Divider(),
        ft.Row([status_text, ft.Container(expand=True), refresh_button]),
        ft.Row([data_count_text, ft.Container(expand=True), last_update_text]),
        ft.Divider(),
        ft.Container(
            content=data_list,
            expand=True,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=5,
        ),
    )
    
    # 初始化数据
    page.run_task(refresh_data)


if __name__ == "__main__":
    print("启动简单监控界面测试...")
    print("请访问 http://localhost:8082 查看界面")
    ft.app(target=main, port=8082, view=ft.AppView.WEB_BROWSER)
