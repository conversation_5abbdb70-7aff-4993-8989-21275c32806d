#!/usr/bin/env python3
"""验证修复的脚本。"""

import asyncio
import aiohttp
from beezer.plugins.utils import get_server_port


async def verify_api():
    """验证API是否正常工作。"""
    print("=== 验证API连接 ===")
    
    try:
        server_port = get_server_port()
        api_url = f"http://localhost:{server_port}/api"
        
        print(f"服务器端口: {server_port}")
        print(f"API URL: {api_url}")
        
        async with aiohttp.ClientSession() as session:
            # 测试状态API
            print("\n--- 测试插件状态API ---")
            async with session.get(f"{api_url}/plugin/status") as response:
                if response.status == 200:
                    data = await response.json()
                    plugins = data.get("plugins", {})
                    print(f"✅ 状态API正常，获取到 {len(plugins)} 个插件")
                    
                    for plugin_id, info in plugins.items():
                        status = info.get("status", "未知")
                        print(f"  - {plugin_id}: {status}")
                else:
                    print(f"❌ 状态API失败: {response.status}")
                    return False
            
            # 测试数据API
            print("\n--- 测试插件数据API ---")
            async with session.get(f"{api_url}/plugin/data?limit=5") as response:
                if response.status == 200:
                    data = await response.json()
                    plugin_data = data.get("plugins", {})
                    total_count = data.get("count", 0)
                    print(f"✅ 数据API正常，总共 {total_count} 条数据")
                    
                    for plugin_id, entries in plugin_data.items():
                        print(f"  - {plugin_id}: {len(entries)} 条数据")
                else:
                    print(f"❌ 数据API失败: {response.status}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ API验证失败: {e}")
        return False


def verify_imports():
    """验证模块导入。"""
    print("\n=== 验证模块导入 ===")
    
    try:
        from beezer.gui.views.monitor_view import MonitorView
        print("✅ MonitorView导入成功")
        
        from beezer.gui.components.data_viewer import DataViewer
        print("✅ DataViewer导入成功")
        
        import flet as ft
        print("✅ Flet导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False


async def main():
    """主函数。"""
    print("开始验证修复...")
    
    # 验证模块导入
    import_success = verify_imports()
    
    if import_success:
        # 验证API连接
        api_success = await verify_api()
        
        if api_success:
            print("\n🎉 所有验证通过！")
            print("\n修复总结:")
            print("1. ✅ 修复了ListView的更新机制，使用page.update()而不是control.update()")
            print("2. ✅ 修复了MonitorView中的页面更新方法")
            print("3. ✅ 修复了DataViewer中的页面更新方法")
            print("4. ✅ 暂时禁用了有问题的自动刷新功能")
            print("5. ✅ 确保了正确的page引用传递")
            print("\n现在GUI界面应该能够正常显示数据了。")
            print("请重新启动GUI应用并点击'刷新'按钮来查看数据。")
        else:
            print("\n❌ API验证失败，请检查主服务是否正常运行")
    else:
        print("\n❌ 模块导入失败，请检查代码语法")


if __name__ == "__main__":
    asyncio.run(main())
