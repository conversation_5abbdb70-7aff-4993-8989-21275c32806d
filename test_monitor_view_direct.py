#!/usr/bin/env python3
"""直接测试监控视图的脚本。"""

import flet as ft
import asyncio
from beezer.gui.views.monitor_view import MonitorView


def main(page: ft.Page):
    """主函数。"""
    page.title = "Beezer 监控视图测试"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 20
    
    # 创建监控视图
    monitor_view = MonitorView(page)
    
    # 添加到页面
    page.add(
        ft.Text("Beezer 监控视图测试", size=24, weight=ft.FontWeight.BOLD),
        ft.Divider(),
        monitor_view.build(),
    )
    
    # 初始化监控视图
    async def init_monitor():
        try:
            await monitor_view.did_mount()
            print("监控视图初始化完成")
        except Exception as e:
            print(f"监控视图初始化失败: {e}")
            import traceback
            traceback.print_exc()
    
    page.run_task(init_monitor)


if __name__ == "__main__":
    print("启动监控视图测试...")
    print("请访问 http://localhost:8083 查看界面")
    ft.app(target=main, port=8083, view=ft.AppView.WEB_BROWSER)
