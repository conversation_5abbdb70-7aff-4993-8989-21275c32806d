#!/usr/bin/env python3
"""测试数据查看器功能的脚本。"""

import asyncio
import aiohttp
import time
from beezer.plugins.utils import get_server_port


async def test_data_viewer_api():
    """测试数据查看器API调用。"""
    server_port = get_server_port()
    api_base_url = f"http://localhost:{server_port}/api"
    
    print(f"=== 测试数据查看器API调用 ===")
    print(f"服务器端口: {server_port}")
    print(f"API基础URL: {api_base_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试状态API
            print("\n--- 测试插件状态API ---")
            async with session.get(f"{api_base_url}/plugin/status") as response:
                if response.status == 200:
                    data = await response.json()
                    plugin_status = data.get("plugins", {})
                    print(f"✅ 状态API正常，获取到 {len(plugin_status)} 个插件状态")
                    for plugin_id, status_info in plugin_status.items():
                        print(f"  - {plugin_id}: {status_info.get('status', '未知')}")
                else:
                    print(f"❌ 状态API失败: {response.status}")
                    return False
            
            # 测试数据API
            print("\n--- 测试插件数据API ---")
            async with session.get(f"{api_base_url}/plugin/data?limit=5") as response:
                if response.status == 200:
                    data = await response.json()
                    plugin_data = data.get("plugins", {})
                    total_count = data.get("count", 0)
                    print(f"✅ 数据API正常，获取到 {total_count} 条数据")
                    
                    # 合并所有数据
                    all_data = []
                    for plugin_id, entries in plugin_data.items():
                        all_data.extend(entries)
                        print(f"  - {plugin_id}: {len(entries)} 条数据")
                    
                    # 按时间戳排序
                    all_data.sort(key=lambda x: x.get("timestamp", 0))
                    
                    if all_data:
                        latest_entry = all_data[-1]
                        print(f"  最新数据: {latest_entry.get('plugin_id')} - {time.strftime('%H:%M:%S', time.localtime(latest_entry.get('timestamp', 0)))}")
                    
                    return True
                else:
                    print(f"❌ 数据API失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False


async def test_data_viewer_simulation():
    """模拟数据查看器的数据处理逻辑。"""
    print(f"\n=== 模拟数据查看器数据处理 ===")
    
    server_port = get_server_port()
    api_base_url = f"http://localhost:{server_port}/api"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{api_base_url}/plugin/data?limit=10") as response:
                if response.status == 200:
                    data = await response.json()
                    plugin_data = data.get("plugins", {})
                    
                    # 模拟DataViewer.update_data的逻辑
                    all_data = []
                    for plugin_id, entries in plugin_data.items():
                        all_data.extend(entries)
                    
                    # 按时间戳排序
                    all_data.sort(key=lambda x: x.get("timestamp", 0))
                    
                    print(f"✅ 数据处理成功，共 {len(all_data)} 条数据")
                    
                    # 显示最新的几条数据
                    if all_data:
                        print("最新的5条数据:")
                        for i, entry in enumerate(all_data[-5:], 1):
                            plugin_id = entry.get("plugin_id", "未知")
                            data_type = entry.get("data_type", "未知")
                            timestamp = entry.get("timestamp", 0)
                            time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))
                            print(f"  {i}. {plugin_id} ({data_type}) - {time_str}")
                    
                    return True
                else:
                    print(f"❌ 数据获取失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 数据处理异常: {e}")
        return False


async def main():
    """主函数。"""
    print("开始测试数据查看器功能...")
    
    # 测试API调用
    api_success = await test_data_viewer_api()
    
    if api_success:
        # 测试数据处理
        processing_success = await test_data_viewer_simulation()
        
        if processing_success:
            print("\n🎉 数据查看器功能测试通过！")
            print("如果GUI界面仍然没有显示数据，可能是以下原因：")
            print("1. GUI界面没有正确调用刷新功能")
            print("2. 数据查看器组件的UI更新有问题")
            print("3. 需要手动点击刷新按钮或开启自动刷新")
        else:
            print("\n❌ 数据处理测试失败")
    else:
        print("\n❌ API调用测试失败")


if __name__ == "__main__":
    asyncio.run(main())
