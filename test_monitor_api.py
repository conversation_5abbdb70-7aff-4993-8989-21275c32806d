#!/usr/bin/env python3
"""测试数据监控API的脚本。

这个脚本会测试监控功能是否正常工作。
"""

import asyncio
import aiohttp
from beezer.config import get_server_port


async def test_plugin_status_api():
    """测试插件状态上报API。"""
    logger.info("测试插件状态上报API")

    # 模拟插件状态数据
    test_plugins = [
        {"id": "modbus_001", "type": "ModbusInbound", "status": "running"},
        {"id": "fanuc_002", "type": "FanucInbound", "status": "running"},
        {"id": "http_003", "type": "HttpClientInbound", "status": "idle"},
        {"id": "xinheyun_out", "type": "XinHeYunOutbound", "status": "running"},
        {"id": "custom_out", "type": "CustomIOTOutbound", "status": "error"},
    ]

    async with aiohttp.ClientSession() as session:
        for plugin in test_plugins:
            data = {
                "plugin_id": plugin["id"],
                "plugin_type": plugin["type"],
                "status": plugin["status"],
                "timestamp": time.time(),
                "details": {
                    "memory_usage": f"{random.randint(10, 100)}MB",
                    "cpu_usage": f"{random.randint(1, 50)}%",
                    "last_activity": time.strftime("%H:%M:%S"),
                },
            }

            try:
                async with session.post(
                    "http://localhost:8000/api/plugin/status", json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"状态上报成功: {plugin['id']} -> {result}")
                    else:
                        logger.error(
                            f"状态上报失败: {plugin['id']} -> {response.status}"
                        )
            except Exception as e:
                logger.error(f"状态上报异常: {plugin['id']} -> {e}")

            await asyncio.sleep(0.1)


async def test_plugin_data_api():
    """测试插件数据上报API。"""
    logger.info("测试插件数据上报API")

    # 模拟数据
    test_data_entries = [
        {
            "plugin_id": "modbus_001",
            "plugin_type": "inbound",
            "data_type": "input",
            "data": {
                "registers": [100, 200, 300, 400],
                "coils": [True, False, True, False],
                "device_id": 1,
                "function_code": 3,
            },
            "metadata": {"queue_size": 5, "response_time": 0.05},
        },
        {
            "plugin_id": "fanuc_002",
            "plugin_type": "inbound",
            "data_type": "input",
            "data": {
                "path": {
                    "axes": [
                        {"name": "X", "position": 123.45, "unit": "mm"},
                        {"name": "Y", "position": 67.89, "unit": "mm"},
                        {"name": "Z", "position": -12.34, "unit": "mm"},
                    ],
                    "spindles": [{"speed": 1500, "load": 75, "unit": "rpm"}],
                },
                "alarms": [],
                "version": 2,
                "result": 0,
            },
            "metadata": {"queue_size": 3, "connection_status": "connected"},
        },
        {
            "plugin_id": "xinheyun_out",
            "plugin_type": "outbound",
            "data_type": "output",
            "data": {
                "gatewayId": "GW001",
                "runtime": 3600,
                "machineStatus": 4,
                "actualQty": 150,
                "runSpeed": 85.5,
            },
            "metadata": {"action": "status", "response_code": 200},
        },
        {
            "plugin_id": "custom_out",
            "plugin_type": "outbound",
            "data_type": "output",
            "data": {
                "equipment_id": "EQ001",
                "status": "running",
                "parameters": {
                    "temperature": 65.2,
                    "pressure": 2.5,
                    "vibration": 0.8,
                },
            },
            "metadata": {"action": "IOTEqStatues", "retry_count": 0},
        },
    ]

    async with aiohttp.ClientSession() as session:
        for i in range(20):  # 发送20轮数据
            for entry in test_data_entries:
                # 添加时间戳和随机变化
                data = entry.copy()
                data["timestamp"] = time.time()

                # 为数据添加一些随机变化
                if "registers" in data["data"]:
                    data["data"]["registers"] = [
                        r + random.randint(-10, 10) for r in data["data"]["registers"]
                    ]
                elif "axes" in data["data"]["path"]:
                    for axis in data["data"]["path"]["axes"]:
                        axis["position"] += random.uniform(-1, 1)
                elif "actualQty" in data["data"]:
                    data["data"]["actualQty"] += random.randint(0, 5)
                elif "temperature" in data["data"]["parameters"]:
                    data["data"]["parameters"]["temperature"] += random.uniform(-2, 2)

                try:
                    async with session.post(
                        "http://localhost:8000/api/plugin/data", json=data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.debug(
                                f"数据上报成功: {data['plugin_id']} -> {result}"
                            )
                        else:
                            logger.error(
                                f"数据上报失败: {data['plugin_id']} -> {response.status}"
                            )
                except Exception as e:
                    logger.error(f"数据上报异常: {data['plugin_id']} -> {e}")

                await asyncio.sleep(0.1)

            logger.info(f"完成第 {i+1} 轮数据上报")
            await asyncio.sleep(2)  # 每轮间隔2秒


async def test_get_apis():
    """测试获取数据的API。"""
    logger.info("测试获取数据API")

    async with aiohttp.ClientSession() as session:
        # 测试获取所有插件状态
        try:
            async with session.get(
                "http://localhost:8000/api/plugin/status"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"获取插件状态成功: 共 {data['count']} 个插件")
                    for plugin_id, status in data["plugins"].items():
                        logger.info(f"  {plugin_id}: {status['status']}")
                else:
                    logger.error(f"获取插件状态失败: {response.status}")
        except Exception as e:
            logger.error(f"获取插件状态异常: {e}")

        # 测试获取所有插件数据
        try:
            async with session.get(
                "http://localhost:8000/api/plugin/data?limit=10"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"获取插件数据成功: 共 {data['count']} 条数据")
                    for plugin_id, entries in data["plugins"].items():
                        logger.info(f"  {plugin_id}: {len(entries)} 条数据")
                else:
                    logger.error(f"获取插件数据失败: {response.status}")
        except Exception as e:
            logger.error(f"获取插件数据异常: {e}")


async def main():
    """主函数。"""
    logger.info("开始测试数据监控API")

    # 首先上报一些状态
    await test_plugin_status_api()

    # 等待一下
    await asyncio.sleep(1)

    # 测试获取API
    await test_get_apis()

    # 开始持续上报数据
    await test_plugin_data_api()


if __name__ == "__main__":
    asyncio.run(main())
